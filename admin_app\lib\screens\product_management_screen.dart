import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import '../widgets/product_management/product_card_admin.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';

class ProductManagementScreen extends StatefulWidget {
  const ProductManagementScreen({super.key});

  @override
  State<ProductManagementScreen> createState() => _ProductManagementScreenState();
}

class _ProductManagementScreenState extends State<ProductManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Data
  List<ProductModel> _products = [];
  List<String> _categories = [];
  bool _isLoading = false;
  bool _hasMoreProducts = true;
  DocumentSnapshot? _lastDocument;
  String _currentFilter = 'all';
  String _selectedCategory = 'all';
  String _searchQuery = '';

  // Statistics
  Map<String, int> _statistics = {
    'total': 0,
    'available': 0,
    'unavailable': 0,
    'featured': 0,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _scrollController.addListener(_onScroll);
    _loadStatistics();
    _loadCategories();
    _loadProducts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      final filters = ['all', 'available', 'unavailable', 'featured'];
      _currentFilter = filters[_tabController.index];
      _refreshProducts();
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreProducts();
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await ProductService.getProductsStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
        });
      }
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await ProductService.getAllCategories();
      if (mounted) {
        setState(() {
          _categories = ['all', ...categories];
        });
      }
    } catch (e) {
      print('Error loading categories: $e');
    }
  }

  Future<void> _loadProducts() async {
    if (_isLoading || !_hasMoreProducts) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final products = await ProductService.getAllProductsPaginated(
        lastDocument: _lastDocument,
        status: _currentFilter,
        category: _selectedCategory,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      if (mounted) {
        setState(() {
          if (_lastDocument == null) {
            _products = products;
          } else {
            _products.addAll(products);
          }
          _hasMoreProducts = products.length >= 20;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('Error loading products: $e');
    }
  }

  Future<void> _loadMoreProducts() async {
    if (!_isLoading && _hasMoreProducts) {
      await _loadProducts();
    }
  }

  void _refreshProducts() {
    setState(() {
      _products.clear();
      _lastDocument = null;
      _hasMoreProducts = true;
    });
    _loadProducts();
    _loadStatistics();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _refreshProducts();
  }

  void _onCategoryChanged(String? category) {
    setState(() {
      _selectedCategory = category ?? 'all';
    });
    _refreshProducts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          _buildFilters(),
          Expanded(
            child: _buildProductsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.shopping_bag_outlined,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          const Text(
            'Product Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeHeading,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: _refreshProducts,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppConstants.surfaceColor,
      child: TabBar(
        controller: _tabController,
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        tabs: [
          Tab(
            text: 'All (${_statistics['total']})',
          ),
          Tab(
            text: 'Available (${_statistics['available']})',
          ),
          Tab(
            text: 'Unavailable (${_statistics['unavailable']})',
          ),
          Tab(
            text: 'Featured (${_statistics['featured']})',
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: AppConstants.surfaceColor,
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search products by name, description, category, or seller...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        _onSearchChanged('');
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                borderSide: const BorderSide(color: AppConstants.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                borderSide: const BorderSide(color: AppConstants.primaryColor),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          // Category filter
          Row(
            children: [
              const Text(
                'Category:',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedCategory,
                  isExpanded: true,
                  items: _categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(
                        category == 'all' ? 'All Categories' : category,
                        style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
                      ),
                    );
                  }).toList(),
                  onChanged: _onCategoryChanged,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList() {
    if (_isLoading && _products.isEmpty) {
      return const LoadingWidget();
    }

    if (_products.isEmpty && !_isLoading) {
      return EmptyStateWidget(
        icon: Icons.shopping_bag_outlined,
        title: 'No Products Found',
        subtitle: _searchQuery.isNotEmpty
            ? 'No products match your search criteria'
            : 'No products available for the selected filter',
        actionText: 'Refresh',
        onAction: _refreshProducts,
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshProducts();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _products.length + (_hasMoreProducts ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _products.length) {
            return const Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: ProductCardAdmin(
              product: _products[index],
              onProductUpdated: () {
                _refreshProducts();
              },
            ),
          );
        },
      ),
    );
  }
}
