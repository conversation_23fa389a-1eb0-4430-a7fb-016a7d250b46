import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../services/user_management_service.dart';
import '../../enums/user_role.dart';
import '../../models/user_model.dart';

class UserStatisticsWidget extends StatefulWidget {
  const UserStatisticsWidget({super.key});

  @override
  State<UserStatisticsWidget> createState() => _UserStatisticsWidgetState();
}

class _UserStatisticsWidgetState extends State<UserStatisticsWidget> {
  Map<String, int>? _statistics;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get overall statistics
      final totalUsersQuery = await UserManagementService.getUsers(limit: 1000);
      final activeUsers = totalUsersQuery.where((user) => user.isActive).length;
      final verifiedUsers = totalUsersQuery.where((user) => user.isVerified).length;
      final resellers = totalUsersQuery.where((user) => user.role == UserRole.reseller).length;
      final admins = totalUsersQuery.where((user) => user.role == UserRole.admin).length;
      final pendingApplications = totalUsersQuery.where((user) =>
          user.resellerApplicationStatus?.value == 'pending').length;

      setState(() {
        _statistics = {
          'totalUsers': totalUsersQuery.length,
          'activeUsers': activeUsers,
          'verifiedUsers': verifiedUsers,
          'resellers': resellers,
          'admins': admins,
          'pendingApplications': pendingApplications,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load statistics: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(AppConstants.paddingLarge),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (_statistics == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: AppConstants.errorColor,
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              const Text('Failed to load statistics'),
              const SizedBox(height: AppConstants.paddingMedium),
              ElevatedButton(
                onPressed: _loadStatistics,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'User Statistics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadStatistics,
                  tooltip: 'Refresh Statistics',
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 3,
              childAspectRatio: 2.5,
              crossAxisSpacing: AppConstants.paddingMedium,
              mainAxisSpacing: AppConstants.paddingMedium,
              children: [
                _buildStatCard(
                  'Total Users',
                  _statistics!['totalUsers']!,
                  Icons.people,
                  AppConstants.primaryColor,
                ),
                _buildStatCard(
                  'Active Users',
                  _statistics!['activeUsers']!,
                  Icons.check_circle,
                  AppConstants.successColor,
                ),
                _buildStatCard(
                  'Verified Users',
                  _statistics!['verifiedUsers']!,
                  Icons.verified,
                  AppConstants.infoColor,
                ),
                _buildStatCard(
                  'Resellers',
                  _statistics!['resellers']!,
                  Icons.store,
                  AppConstants.warningColor,
                ),
                _buildStatCard(
                  'Admins',
                  _statistics!['admins']!,
                  Icons.admin_panel_settings,
                  AppConstants.errorColor,
                ),
                _buildStatCard(
                  'Pending Applications',
                  _statistics!['pendingApplications']!,
                  Icons.pending,
                  Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, int value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            value.toString(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
